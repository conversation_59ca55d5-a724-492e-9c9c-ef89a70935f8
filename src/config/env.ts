/**
 * 统一的环境配置管理模块
 * 
 * 这个模块负责：
 * 1. 根据 ENV_FILE 环境变量动态加载不同的配置文件
 * 2. 提供统一的环境变量访问接口
 * 3. 确保配置只加载一次，避免重复加载
 * 4. 提供配置验证和默认值处理
 */

import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { logger } from '../utils/logger';

// 配置加载状态
let isConfigLoaded = false;
let loadedEnvFile = '';

/**
 * 加载环境配置
 * @param envFile 指定的环境文件路径，如果不提供则使用 ENV_FILE 环境变量
 * @param force 是否强制重新加载配置
 */
export function loadEnvConfig(envFile?: string, force: boolean = false): void {
  // 如果已经加载过且不是强制重新加载，则跳过
  if (isConfigLoaded && !force) {
    return;
  }

  // 确定要加载的环境文件
  const targetEnvFile = envFile || process.env.ENV_FILE || '.env';
  
  // 解析文件路径（相对于项目根目录）
  const rootDir = path.resolve(__dirname, '../..');
  const envFilePath = path.resolve(rootDir, targetEnvFile);

  // 检查文件是否存在
  if (!fs.existsSync(envFilePath)) {
    logger.warn(`⚠️  环境配置文件不存在: ${envFilePath}`);
    logger.warn(`   尝试使用默认配置文件: .env`);
    
    // 尝试加载默认的 .env 文件
    const defaultEnvPath = path.resolve(rootDir, '.env');
    if (fs.existsSync(defaultEnvPath)) {
      dotenv.config({ path: defaultEnvPath });
      loadedEnvFile = '.env';
    } else {
      logger.warn(`   默认配置文件也不存在，使用系统环境变量`);
      loadedEnvFile = 'system';
    }
  } else {
    // 加载指定的环境文件
    const result = dotenv.config({ path: envFilePath });
    
    if (result.error) {
      logger.error(`❌ 加载环境配置文件失败: ${envFilePath}`);
      logger.error(`   错误信息: ${result.error.message}`);
      throw result.error;
    }
    
    loadedEnvFile = targetEnvFile;
  }

  isConfigLoaded = true;
  
  // 尝试加载日志配置文件
  loadLoggerConfig();

  // 重新加载 Logger 配置以应用新的环境变量
  try {
    logger.reloadConfig();
  } catch (error) {
    // 如果 logger 还没有初始化，忽略错误
  }

  // 输出加载信息（仅在开发环境）
  if (process.env.NODE_ENV === 'development') {
    logger.info(`🔧 环境配置已加载: ${loadedEnvFile}`);
  }
}

/**
 * 获取当前加载的环境文件信息
 */
export function getEnvInfo(): { isLoaded: boolean; envFile: string } {
  return {
    isLoaded: isConfigLoaded,
    envFile: loadedEnvFile
  };
}

/**
 * 获取环境变量值，支持默认值和类型转换
 */
export function getEnvVar(key: string, defaultValue?: string): string | undefined {
  return process.env[key] || defaultValue;
}

/**
 * 获取数字类型的环境变量
 */
export function getEnvNumber(key: string, defaultValue?: number): number {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`环境变量 ${key} 未设置且没有默认值`);
  }
  
  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    throw new Error(`环境变量 ${key} 不是有效的数字: ${value}`);
  }
  
  return parsed;
}

/**
 * 获取布尔类型的环境变量
 */
export function getEnvBoolean(key: string, defaultValue?: boolean): boolean {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`环境变量 ${key} 未设置且没有默认值`);
  }
  
  return value.toLowerCase() === 'true';
}

/**
 * 验证必需的环境变量
 */
export function validateRequiredEnvVars(requiredVars: string[]): void {
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    logger.error(`❌ 缺少必需的环境变量:`);
    missingVars.forEach(varName => {
      logger.error(`   - ${varName}`);
    });
    throw new Error(`缺少必需的环境变量: ${missingVars.join(', ')}`);
  }
}

/**
 * 加载日志配置文件
 */
function loadLoggerConfig(): void {
  const rootDir = path.resolve(__dirname, '../..');

  // 根据当前加载的环境文件智能选择日志配置文件
  const possibleLoggerConfigs = getLoggerConfigFiles();

  for (const configFile of possibleLoggerConfigs) {
    const loggerConfigPath = path.resolve(rootDir, configFile);

    if (fs.existsSync(loggerConfigPath)) {
      const result = dotenv.config({ path: loggerConfigPath });
      if (!result.error) {
        if (process.env.NODE_ENV === 'development') {
          logger.info(`🔧 日志配置已加载: ${configFile}`);
        }
        return; // 成功加载后退出
      }
    }
  }

  // 如果没有找到任何日志配置文件，输出提示信息
  if (process.env.NODE_ENV === 'development') {
    logger.warn('⚠️  未找到日志配置文件，使用默认配置');
  }
}

/**
 * 根据当前环境文件获取对应的日志配置文件列表
 */
function getLoggerConfigFiles(): string[] {
  const configs: string[] = [];

  // 根据当前加载的环境文件确定对应的日志配置文件
  if (loadedEnvFile) {
    if (loadedEnvFile.includes('kaia')) {
      configs.push('.env.logging.kaia');
    } else if (loadedEnvFile.includes('pharos')) {
      configs.push('.env.logging.pharos');
    }
  }

  // 根据 ENV_FILE 环境变量确定日志配置文件
  const envFile = process.env.ENV_FILE;
  if (envFile) {
    if (envFile.includes('kaia')) {
      configs.push('.env.logging.kaia');
    } else if (envFile.includes('pharos')) {
      configs.push('.env.logging.pharos');
    }
  }

  // 添加通用的日志配置文件作为备选
  configs.push('.env.logging');  // 通用日志配置文件
  configs.push('.env.logger');   // 原来的日志配置文件名

  // 去重并返回
  return [...new Set(configs)];
}

/**
 * 重置配置状态（主要用于测试）
 */
export function resetEnvConfig(): void {
  isConfigLoaded = false;
  loadedEnvFile = '';
}

// 自动加载配置（当模块被导入时）
loadEnvConfig();
