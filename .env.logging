# 统一日志管理系统配置
# 根据当前环境调整以下配置

# ================================
# 日志级别配置
# ================================
# 可选值: ERROR, WARN, INFO, DEBUG
# ERROR: 只显示错误信息
# WARN: 显示警告和错误信息
# INFO: 显示信息、警告和错误信息（默认）
# DEBUG: 显示所有级别的日志信息
LOG_LEVEL=INFO

# ================================
# 日志输出格式配置
# ================================
# 是否启用彩色输出（默认: 开发环境true，生产环境false）
LOG_COLORS=true

# 是否显示时间戳（默认: true）
LOG_TIMESTAMP=true

# 是否使用JSON格式输出（默认: 开发环境false，生产环境true）
# true: 结构化JSON格式，适合日志收集系统
# false: 人类可读格式，适合开发调试
LOG_JSON=false

# 是否启用控制台输出（默认: true）
# 设置为false可以完全禁用控制台日志输出
LOG_CONSOLE=true

# 是否完全禁用日志输出（默认: false）
# 设置为true可以完全禁用所有日志输出，优先级最高
LOG_DISABLED=false

# ================================
# 环境特定配置建议
# ================================

# 开发环境推荐配置：
# LOG_LEVEL=DEBUG
# LOG_COLORS=true
# LOG_TIMESTAMP=true
# LOG_JSON=false
# LOG_CONSOLE=true
# LOG_DISABLED=false

# 生产环境推荐配置：
# LOG_LEVEL=ERROR
# LOG_COLORS=false
# LOG_TIMESTAMP=true
# LOG_JSON=true
# LOG_CONSOLE=true
# LOG_DISABLED=false

# 测试环境推荐配置：
# LOG_LEVEL=WARN
# LOG_COLORS=false
# LOG_TIMESTAMP=true
# LOG_JSON=true
# LOG_CONSOLE=true
# LOG_DISABLED=false
